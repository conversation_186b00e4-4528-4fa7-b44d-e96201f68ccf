package com.ously.gamble.collectibles.controller;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.dto.CardCollectionMapper;
import com.ously.gamble.collectibles.exception.CollectiblesLogicException;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.service.CollectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.Set;

@RestController
@RequestMapping("/api/admin/collections")
@Tag(name = "Admin Collections", description = "Admin operations for collections, cards and rewards")
public class AdminController {

    private static final Logger log = LoggerFactory.getLogger(AdminController.class);

    private final CollectionService collectionService;
    private final CardCollectionMapper mapper;

    public AdminController(CollectionService collectionService, CardCollectionMapper mapper) {
        this.collectionService = collectionService;
        this.mapper = mapper;
    }

    // === COLLECTION OPERATIONS ===
    @Operation(description = "Create collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse createCollection(
            @Valid @RequestBody CardCollectionDto.CreateCardCollectionRequest request) throws CollectiblesLogicException {
        CardCollection collection = collectionService.create(request);
        return mapper.toResponse(collection);
    }

    @Operation(description = "Update collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionResponse> updateCollection(
            @PathVariable Integer id,
            @Valid @RequestBody CardCollectionDto.UpdateCardCollectionRequest request) throws CollectiblesLogicException {
        var request = new CardCollectionDto.UpdateCardCollectionRequest(
                id, // 👈 из URL
                request.name(),
                request.startDate(),
                request.endDate(),
                request.status(),
                request.sortOrder()
        );


        Optional<CardCollection> collection = collectionService.update(id, request);

        if (collection.isEmpty()) return ResponseEntity.notFound().build();
        CardCollection fullCollection = collectionService.findByIdWithCardsAndRewards(collection.get().getId()).orElseThrow();
        return ResponseEntity.ok(mapper.toResponse(fullCollection));
    }
//
//    @Operation(description = "Delete collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @DeleteMapping("/{id}")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<Void> deleteCollection(@PathVariable Integer id) {
//        if (collectionService.delete(id)) {
//            return ResponseEntity.noContent().build();
//        }
//        return ResponseEntity.notFound().build();
//    }
//
//    @Operation(description = "Get all collections", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping
//    @RolesAllowed("ADMIN")
//    public Page<CardCollectionDto.CardCollectionSummaryResponse> getCollections(
//            @PageableDefault(size = 20) Pageable pageable) {
//        return collectionService.findAll(pageable)
//                .map(mapper::toSummaryResponse);
//    }
//
//    @Operation(description = "Get collection details", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/{id}")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<CardCollectionDto.CardCollectionResponse> getCollection(@PathVariable Integer id) {
//        return collectionService.findByIdWithCardsAndRewards(id)
//                .map(collection -> ResponseEntity.ok(mapper.toResponse(collection)))
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    // === CARD OPERATIONS ===
//
//    @Operation(description = "Create card in collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @PostMapping("/{collectionId}/cards")
//    @RolesAllowed("ADMIN")
//    public CardCollectionDto.CardCollectionResponse createCard(
//            @PathVariable Integer collectionId,
//            @Valid @RequestBody CardCollectionDto.CreateCardRequest request) {
//        CardCollection collection = cardCollectionService.createCard(collectionId, request);
//        return mapper.toResponse(collection);
//    }
//
//    @Operation(description = "Update card", security = {@SecurityRequirement(name = "bearer-key")})
//    @PutMapping("/{collectionId}/cards/{cardId}")
//    @RolesAllowed("ADMIN")
//    public CardCollectionDto.CardCollectionResponse updateCard(
//            @PathVariable Integer collectionId,
//            @PathVariable Integer cardId,
//            @Valid @RequestBody CardCollectionDto.UpdateCardRequest request) {
//        CardCollection collection = cardCollectionService.updateCard(collectionId, cardId, request);
//        return mapper.toResponse(collection);
//    }
//
//    @Operation(description = "Delete card", security = {@SecurityRequirement(name = "bearer-key")})
//    @DeleteMapping("/{collectionId}/cards/{cardId}")
//    @RolesAllowed("ADMIN")
//    public CardCollectionDto.CardCollectionResponse deleteCard(
//            @PathVariable Integer collectionId,
//            @PathVariable Integer cardId) {
//        CardCollection collection = cardCollectionService.deleteCard(collectionId, cardId);
//        return mapper.toResponse(collection);
//    }
//
//    // === REWARD OPERATIONS ===
//
//    @Operation(description = "Create reward for collection or card", security = {@SecurityRequirement(name = "bearer-key")})
//    @PostMapping("/{collectionId}/rewards")
//    @RolesAllowed("ADMIN")
//    public CardCollectionDto.CardCollectionResponse createReward(
//            @PathVariable Integer collectionId,
//            @Valid @RequestBody CardCollectionDto.CreateRewardRequest request) {
//        CardCollection collection = collectionService.createReward(collectionId, request);
//        return mapper.toResponse(collection);
//    }
//
//    @Operation(description = "Update reward", security = {@SecurityRequirement(name = "bearer-key")})
//    @PutMapping("/{collectionId}/rewards/{rewardId}")
//    @RolesAllowed("ADMIN")
//    public CardCollectionDto.CardCollectionResponse updateReward(
//            @PathVariable Integer collectionId,
//            @PathVariable Integer rewardId,
//            @Valid @RequestBody CardCollectionDto.UpdateRewardRequest request) {
//        CardCollection collection = collectionService.updateReward(collectionId, rewardId, request);
//        return mapper.toResponse(collection);
//    }
//
//    @Operation(description = "Delete reward", security = {@SecurityRequirement(name = "bearer-key")})
//    @DeleteMapping("/{collectionId}/rewards/{rewardId}")
//    @RolesAllowed("ADMIN")
//    public CardCollectionDto.CardCollectionResponse deleteReward(
//            @PathVariable Integer collectionId,
//            @PathVariable Integer rewardId) {
//        CardCollection collection = collectionService.deleteReward(collectionId, rewardId);
//        return mapper.toResponse(collection);
//    }
}
